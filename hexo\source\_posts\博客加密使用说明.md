---
title: 博客加密使用说明
date: 2025-01-16 13:00:00
categories: 
- 教程
tags: 
- Hexo
- 加密
- 教程
cover: https://s21.ax1x.com/2025/07/26/pVJhiSH.jpg
---

本博客已配置 hexo-blog-encrypt 插件，支持文章加密功能。

<!-- more -->

## 加密方式

### 1. 单篇文章加密

在文章的 Front Matter 中添加以下字段：

```yaml
---
title: 文章标题
password: 你的密码
abstract: 摘要信息（可选）
message: 密码提示信息（可选）
wrong_pass_message: 密码错误提示（可选）
wrong_hash_message: 校验失败提示（可选）
---
```

### 2. 标签批量加密

在 `_config.yml` 中配置：

```yaml
encrypt:
  tags:
  - {name: 标签名, password: 密码}
```

当文章包含指定标签时，会自动应用加密。

## 当前配置

### 全局设置
- 默认摘要：有东西被加密了, 请输入密码查看.
- 默认提示：请输入密码查看内容
- 密码错误提示：抱歉, 这个密码看着不太对, 请再试试.

### 标签加密
- 标签 `FE`：密码 `123456`

## 使用注意事项

1. **密码安全**：请使用足够复杂的密码
2. **SEO影响**：加密文章的内容不会被搜索引擎索引
3. **兼容性**：确保主题支持加密功能的样式
4. **部署**：配置完成后直接部署到 GitHub Pages 即可生效

## 示例文章

- [加密文章示例](/加密文章示例/) - 使用密码 `123456`
- [标签加密示例](/标签加密示例/) - 包含 FE 标签，密码 `123456`
